const http = require('http');
const https = require('https');
const url = require('url');
const net = require('net');

class ProxyServer {
    constructor(port = 8090, host = '0.0.0.0') {
        this.port = port;
        this.host = host;
        this.server = null;
    }

    // 处理HTTP请求
    handleHttpRequest(req, res) {
        const targetUrl = req.url;
        const parsedUrl = url.parse(targetUrl);
        
        console.log(`[HTTP] ${req.method} ${targetUrl}`);

        const options = {
            hostname: parsedUrl.hostname,
            port: parsedUrl.port || 80,
            path: parsedUrl.path,
            method: req.method,
            headers: req.headers
        };

        // 删除代理相关的headers
        delete options.headers['proxy-connection'];
        delete options.headers['proxy-authorization'];

        const proxyReq = http.request(options, (proxyRes) => {
            // 设置响应头
            res.writeHead(proxyRes.statusCode, proxyRes.headers);
            proxyRes.pipe(res);
        });

        proxyReq.on('error', (err) => {
            console.error(`[HTTP Error] ${err.message}`);
            res.writeHead(500);
            res.end('Proxy Error: ' + err.message);
        });

        req.pipe(proxyReq);
    }

    // 处理HTTPS CONNECT请求
    handleHttpsConnect(req, clientSocket, head) {
        const { hostname, port } = url.parse(`http://${req.url}`);
        const targetPort = port || 443;
        
        console.log(`[HTTPS] CONNECT ${hostname}:${targetPort}`);

        const serverSocket = net.connect(targetPort, hostname, () => {
            clientSocket.write('HTTP/1.1 200 Connection Established\r\n\r\n');
            serverSocket.write(head);
            serverSocket.pipe(clientSocket);
            clientSocket.pipe(serverSocket);
        });

        serverSocket.on('error', (err) => {
            console.error(`[HTTPS Error] ${err.message}`);
            clientSocket.write('HTTP/1.1 500 Connection Error\r\n\r\n');
            clientSocket.end();
        });

        clientSocket.on('error', (err) => {
            console.error(`[Client Error] ${err.message}`);
            serverSocket.end();
        });
    }

    start() {
        this.server = http.createServer((req, res) => {
            this.handleHttpRequest(req, res);
        });

        // 处理HTTPS CONNECT方法
        this.server.on('connect', (req, clientSocket, head) => {
            this.handleHttpsConnect(req, clientSocket, head);
        });

        this.server.listen(this.port, this.host, () => {
            console.log(`代理服务器启动成功！`);
            console.log(`监听地址: ${this.host}:${this.port}`);
            console.log(`HTTP代理地址: http://${this.host}:${this.port}`);
            console.log(`HTTPS代理地址: http://${this.host}:${this.port}`);
            console.log('按 Ctrl+C 停止服务器');
        });

        // 优雅关闭
        process.on('SIGINT', () => {
            console.log('\n正在关闭代理服务器...');
            this.server.close(() => {
                console.log('代理服务器已关闭');
                process.exit(0);
            });
        });
    }
}

// 启动代理服务器
const config = require('./config');
const proxy = new ProxyServer(config.port, config.host);
proxy.start();
