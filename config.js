// 代理服务器配置文件
module.exports = {
    // 代理服务器监听端口
    port: 8090,

    // 代理服务器监听地址 (0.0.0.0 表示监听所有网络接口)
    host: '0.0.0.0',
    
    // 日志配置
    logging: {
        enabled: true,
        logRequests: true,
        logErrors: true
    },
    
    // 超时设置（毫秒）
    timeout: 30000,
    
    // 允许的请求方法
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS', 'PATCH'],
    
    // 黑名单域名（可选）
    blockedDomains: [
        // 'example.com',
        // 'blocked-site.com'
    ],
    
    // 白名单域名（如果设置，只允许访问这些域名）
    allowedDomains: [
        // 'allowed-site.com'
    ]
};
