# HTTP代理服务器

一个简单的Node.js HTTP/HTTPS代理服务器，用于转发本地请求。

## 功能特性

- 支持HTTP和HTTPS网站访问
- 自动处理CONNECT方法用于HTTPS隧道
- 请求日志记录
- 错误处理和优雅关闭
- 可配置的端口和设置

## 快速开始

1. 安装依赖：
```bash
npm install
```

2. 启动代理服务器：
```bash
npm start
```

3. 配置浏览器或应用程序使用代理：
   - HTTP代理: `服务器IP:8090`
   - HTTPS代理: `服务器IP:8090`

## 配置

编辑 `config.js` 文件来修改代理服务器设置：
- 端口号
- 日志选项
- 超时设置
- 域名黑白名单

## 使用示例

### 浏览器设置
在浏览器中设置HTTP代理为 `服务器IP:8090`

### curl命令
```bash
curl --proxy http://服务器IP:8090 http://example.com
curl --proxy http://服务器IP:8090 https://example.com
```

## 注意事项

- 默认监听端口8090
- 监听所有网络接口(0.0.0.0)，支持外部访问
- 支持HTTP和HTTPS协议
- 按Ctrl+C可优雅关闭服务器
